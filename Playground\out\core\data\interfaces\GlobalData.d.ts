export interface CoreGlobalData {
    serverVersion: string;
    maintenanceMode: boolean;
    lastUpdated: number;
}
export interface BaseGlobalData extends CoreGlobalData {
    gameData: Record<string, unknown>;
    serverConfig: ServerConfig;
}
export interface ServerConfig {
    maxPlayers: number;
    autoSaveInterval: number;
    debugMode: boolean;
    [key: string]: unknown;
}
export type GameGlobalData<T = Record<string, unknown>> = BaseGlobalData & {
    gameData: T;
};
