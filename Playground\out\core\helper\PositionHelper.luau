-- Compiled with roblox-ts v3.0.0
local PositionHelper
do
	PositionHelper = setmetatable({}, {
		__tostring = function()
			return "PositionHelper"
		end,
	})
	PositionHelper.__index = PositionHelper
	function PositionHelper.new(...)
		local self = setmetatable({}, PositionHelper)
		return self:constructor(...) or self
	end
	function PositionHelper:constructor()
	end
	function PositionHelper:setPosition(instance, position, rotation)
		if instance:IsA("BasePart") then
			instance.Position = position
			if rotation then
				instance.Rotation = rotation
			end
		elseif instance:IsA("Model") then
			PositionHelper:ensurePrimaryPart(instance, position, rotation)
		end
	end
	function PositionHelper:ensurePrimaryPart(model, position, rotation)
		if not model.PrimaryPart then
			local primaryPart = Instance.new("Part")
			primaryPart.Name = "PrimaryPart"
			primaryPart.Anchored = true
			primaryPart.Transparency = 1
			primaryPart.CanCollide = false
			primaryPart.Size = Vector3.new(0.1, 0.1, 0.1)
			primaryPart.Parent = model
			model.PrimaryPart = primaryPart
		end
		local primaryPart = model.PrimaryPart
		primaryPart.Position = position
		if rotation then
			primaryPart.Rotation = rotation
		end
		return primaryPart
	end
	function PositionHelper:moveModel(model, position)
		if model.PrimaryPart then
			model.PrimaryPart.Position = position
		else
			PositionHelper:ensurePrimaryPart(model, position)
		end
	end
	function PositionHelper:rotateModel(model, rotation)
		if model.PrimaryPart then
			model.PrimaryPart.Rotation = rotation
		end
	end
	function PositionHelper:getPosition(instance)
		if instance:IsA("BasePart") then
			return instance.Position
		elseif instance:IsA("Model") and instance.PrimaryPart then
			return instance.PrimaryPart.Position
		end
		return Vector3.new(0, 0, 0)
	end
	function PositionHelper:getRotation(instance)
		if instance:IsA("BasePart") then
			return instance.Rotation
		elseif instance:IsA("Model") and instance.PrimaryPart then
			return instance.PrimaryPart.Rotation
		end
		return Vector3.new(0, 0, 0)
	end
	function PositionHelper:offsetPosition(instance, offset)
		local currentPos = PositionHelper:getPosition(instance)
		local _offset = offset
		local newPos = currentPos + _offset
		PositionHelper:setPosition(instance, newPos)
		return newPos
	end
	function PositionHelper:lookAt(instance, target)
		local currentPos = PositionHelper:getPosition(instance)
		local direction = (target - currentPos).Unit
		local lookCFrame = CFrame.lookAt(currentPos, target)
		if instance:IsA("BasePart") then
			instance.CFrame = lookCFrame
		elseif instance:IsA("Model") and instance.PrimaryPart then
			instance.PrimaryPart.CFrame = lookCFrame
		end
	end
	function PositionHelper:randomizePosition(instance, range)
		local currentPos = PositionHelper:getPosition(instance)
		local randomOffset = Vector3.new((math.random() - 0.5) * range.X, (math.random() - 0.5) * range.Y, (math.random() - 0.5) * range.Z)
		local newPos = currentPos + randomOffset
		PositionHelper:setPosition(instance, newPos)
		return newPos
	end
	function PositionHelper:snapToGrid(instance, gridSize)
		if gridSize == nil then
			gridSize = 4
		end
		local currentPos = PositionHelper:getPosition(instance)
		local snappedPos = Vector3.new(math.floor(currentPos.X / gridSize) * gridSize, math.floor(currentPos.Y / gridSize) * gridSize, math.floor(currentPos.Z / gridSize) * gridSize)
		PositionHelper:setPosition(instance, snappedPos)
		return snappedPos
	end
	function PositionHelper:isInRange(instance1, instance2, range)
		local pos1 = PositionHelper:getPosition(instance1)
		local pos2 = PositionHelper:getPosition(instance2)
		return (pos1 - pos2).Magnitude <= range
	end
	function PositionHelper:getDistance(instance1, instance2)
		local pos1 = PositionHelper:getPosition(instance1)
		local pos2 = PositionHelper:getPosition(instance2)
		return (pos1 - pos2).Magnitude
	end
	function PositionHelper:canMoveTo(from, to, ignoreList)
		local _to = to
		local _from = from
		local direction = _to - _from
		local distance = direction.Magnitude
		if distance < 0.1 then
			return true
		end
		local raycastParams = RaycastParams.new()
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
		raycastParams.FilterDescendantsInstances = ignoreList or {}
		local result = game.Workspace:Raycast(from, direction, raycastParams)
		-- If we hit something before reaching the target, path is blocked
		return not result or result.Distance >= distance
	end
	function PositionHelper:findClearPath(from, to, ignoreList)
		-- If direct path is clear, use it
		if PositionHelper:canMoveTo(from, to, ignoreList) then
			return to
		end
		-- Try simple avoidance by going around obstacles
		local _to = to
		local _from = from
		local direction = (_to - _from).Unit
		local rightDirection = Vector3.new(-direction.Z, 0, direction.X)
		local leftDirection = Vector3.new(direction.Z, 0, -direction.X)
		local avoidanceDistance = 5
		-- Try going right around obstacle
		local _from_1 = from
		local _arg0 = rightDirection * avoidanceDistance
		local rightTarget = _from_1 + _arg0
		if PositionHelper:canMoveTo(from, rightTarget, ignoreList) then
			return rightTarget
		end
		-- Try going left around obstacle
		local _from_2 = from
		local _arg0_1 = leftDirection * avoidanceDistance
		local leftTarget = _from_2 + _arg0_1
		if PositionHelper:canMoveTo(from, leftTarget, ignoreList) then
			return leftTarget
		end
		-- If all else fails, return original target
		return to
	end
	function PositionHelper:hasLineOfSight(from, to, ignoreList)
		local _to = to
		local _from = from
		local direction = _to - _from
		local distance = direction.Magnitude
		if distance < 0.1 then
			return true
		end
		local raycastParams = RaycastParams.new()
		raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
		raycastParams.FilterDescendantsInstances = ignoreList or {}
		local result = game.Workspace:Raycast(from, direction, raycastParams)
		-- Line of sight is clear if we don't hit anything or hit something beyond the target
		return not result or result.Distance >= distance
	end
end
return {
	PositionHelper = PositionHelper,
}
