-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local RunService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").RunService
local EntityType = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "enums", "EntityType").EntityType
local EffectPartBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder").EffectPartBuilder
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
local createParticleExplosion = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "ParticleHelper").createParticleExplosion
local playSound = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper").playSound
local _VisualEffectUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "VisualEffectUtils")
local cameraShake = _VisualEffectUtils.cameraShake
local createImpactFlash = _VisualEffectUtils.createImpactFlash
local createTrail = _VisualEffectUtils.createTrail
local TrailHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "TrailHelper").TrailHelper
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local CharacterBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "character", "CharacterBuilder").CharacterBuilder
local EntityManager
do
	EntityManager = setmetatable({}, {
		__tostring = function()
			return "EntityManager"
		end,
	})
	EntityManager.__index = EntityManager
	function EntityManager.new(...)
		local self = setmetatable({}, EntityManager)
		return self:constructor(...) or self
	end
	function EntityManager:constructor()
		self.entities = {}
		self.entityCounter = 0
		self:startHeartbeat()
	end
	function EntityManager:getInstance()
		if not EntityManager.instance then
			EntityManager.instance = EntityManager.new()
		end
		return EntityManager.instance
	end
	function EntityManager:spawnEntity(options)
		local id = self:generateEntityId()
		local entity = {
			id = id,
			type = options.type,
			instance = self:createEntityInstance(options),
			position = options.position,
			isActive = true,
			createdAt = tick(),
			data = options.data or {},
		}
		self.entities[id] = entity
		-- Auto-destroy after lifetime if specified
		local _value = options.lifetime
		if _value ~= 0 and _value == _value and _value then
			task.delay(options.lifetime, function()
				self:destroyEntity(id)
			end)
		end
		return entity
	end
	function EntityManager:getEntity(id)
		local _entities = self.entities
		local _id = id
		return _entities[_id]
	end
	function EntityManager:getEntitiesByType(entityType)
		local result = {}
		local _exp = self.entities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(entity)
			if entity.type == entityType and entity.isActive then
				local _entity = entity
				table.insert(result, _entity)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return result
	end
	function EntityManager:getEntitiesInRadius(position, radius)
		local result = {}
		local _exp = self.entities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(entity)
			local _condition = entity.isActive
			if _condition then
				local _position = entity.position
				local _position_1 = position
				_condition = (_position - _position_1).Magnitude <= radius
			end
			if _condition then
				local _entity = entity
				table.insert(result, _entity)
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return result
	end
	function EntityManager:updateEntityPosition(id, position)
		local _entities = self.entities
		local _id = id
		local entity = _entities[_id]
		if entity and entity.isActive then
			entity.position = position
			PositionHelper:setPosition(entity.instance, position)
		end
	end
	function EntityManager:updateEntityData(id, data)
		local _entities = self.entities
		local _id = id
		local entity = _entities[_id]
		if entity and entity.isActive then
			local _object = {}
			local _spread = entity.data
			if _spread then
				for _k, _v in _spread do
					_object[_k] = _v
				end
			end
			for _k, _v in data do
				_object[_k] = _v
			end
			entity.data = _object
		end
	end
	function EntityManager:destroyEntity(id)
		local _entities = self.entities
		local _id = id
		local entity = _entities[_id]
		if not entity then
			return false
		end
		entity.isActive = false
		if entity.instance.Parent then
			entity.instance:Destroy()
		end
		local _entities_1 = self.entities
		local _id_1 = id
		_entities_1[_id_1] = nil
		return true
	end
	function EntityManager:getActiveEntityCount()
		local count = 0
		local _exp = self.entities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(entity)
			if entity.isActive then
				count += 1
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return count
	end
	function EntityManager:cleanup()
		local _exp = self.entities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(entity)
			if entity.instance.Parent then
				entity.instance:Destroy()
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.entities)
		if self.heartbeatConnection then
			self.heartbeatConnection:Disconnect()
		end
	end
	function EntityManager:generateEntityId()
		self.entityCounter += 1
		return `entity_{self.entityCounter}_{tick()}`
	end
	function EntityManager:createEntityInstance(options)
		local instance
		local _exp = options.type
		repeat
			if _exp == (EntityType.Player) then
				-- Use CharacterBuilder for clean player creation
				instance = CharacterBuilder:createBasicPlayer("PlayerEntity", options.position)
				break
			end
			if _exp == (EntityType.NPC) then
				-- Use CharacterBuilder for clean NPC creation
				local npc = CharacterBuilder:createBasicNPC("NPC", options.position)
				-- Play spawn sound effect
				playSound("rbxassetid://131961136", 0.5, 1, npc)
				instance = npc
				break
			end
			if _exp == (EntityType.Projectile) then
				-- Create projectile with core helpers
				local projectile = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(1, 1, 1)):color(Color3.fromRGB(255, 100, 100)):material(Enum.Material.Neon):transparency(0.2):position(options.position):spawn()
				-- Add trail effect using TrailHelper
				TrailHelper:createProjectileTrail(projectile, Color3.fromRGB(255, 100, 100))
				-- Add pulsing animation using EffectTweenBuilder
				EffectTweenBuilder["for"](EffectTweenBuilder, projectile):duration(0.5):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):expand(Vector3.new(1.2, 1.2, 1.2)):fade(0.1):play()
				-- Add visual trail effect using core helper
				task.delay(0.1, function()
					if projectile.Parent then
						local _position = projectile.Position
						local _vector3 = Vector3.new(math.random(-5, 5), math.random(-2, 2), math.random(-5, 5))
						local endPos = _position + _vector3
						createTrail(projectile.Position, endPos, Color3.fromRGB(255, 100, 100), 6)
					end
				end)
				-- Play projectile sound
				playSound("rbxassetid://5127701483", 0.4, 1.2, projectile)
				instance = projectile
				break
			end
			if _exp == (EntityType.Effect) then
				-- Create effect with core helpers
				local effect = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(2, 2, 2)):color(Color3.fromRGB(100, 255, 100)):material(Enum.Material.ForceField):transparency(0.5):position(options.position):spawn()
				-- Create particle explosion effect
				createParticleExplosion(options.position, 10, Color3.fromRGB(100, 255, 100), { 5, 15 }, { 0.2, 0.8 })
				-- Create impact flash
				createImpactFlash(options.position, 5, 0.5)
				-- Add subtle camera shake
				cameraShake(2, 0.3)
				-- Add expanding animation using EffectTweenBuilder
				local _ = EffectTweenBuilder["for"](EffectTweenBuilder, effect)
				local _condition = options.lifetime
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 3
				end
				_:duration(_condition):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):expand(Vector3.new(10, 10, 10)):fade(1):onComplete(function()
					if effect.Parent then
						effect:Destroy()
					end
				end):play()
				-- Play effect sound
				playSound("rbxassetid://131961136", 0.3, 1.5, effect)
				instance = effect
				break
			end
			if _exp == (EntityType.Pickup) then
				-- Create pickup with core helpers
				local pickup = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(2, 2, 2)):color(Color3.fromRGB(255, 255, 100)):material(Enum.Material.Neon):transparency(0.1):position(options.position):spawn()
				-- Add floating animation using EffectTweenBuilder
				local floatHeight = 2
				local floatSpeed = 2
				-- Create continuous floating motion
				local floatUp
				floatUp = function()
					local _ = EffectTweenBuilder["for"](EffectTweenBuilder, pickup):duration(floatSpeed):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
					local _position = options.position
					local _vector3 = Vector3.new(0, floatHeight, 0)
					_:move(_position + _vector3):onComplete(function()
						-- Float back down
						local _1 = EffectTweenBuilder["for"](EffectTweenBuilder, pickup):duration(floatSpeed):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
						local _position_1 = options.position
						local _vector3_1 = Vector3.new(0, floatHeight, 0)
						_1:move(_position_1 - _vector3_1):onComplete(floatUp):play()
					end):play()
				end
				floatUp()
				-- Add spinning animation
				local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
				bodyAngularVelocity.AngularVelocity = Vector3.new(0, 2, 0)
				bodyAngularVelocity.MaxTorque = Vector3.new(0, math.huge, 0)
				bodyAngularVelocity.Parent = pickup
				-- Add pulsing glow effect
				EffectTweenBuilder["for"](EffectTweenBuilder, pickup):duration(1):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):brightness(2):fade(0.3):onComplete(function()
					-- Pulse back
					EffectTweenBuilder["for"](EffectTweenBuilder, pickup):duration(1):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):brightness(1):fade(0.1):play()
				end):play()
				-- Play pickup spawn sound
				playSound("rbxassetid://5127701483", 0.3, 0.8, pickup)
				instance = pickup
				break
			end
			if _exp == (EntityType.Structure) then
				-- Create structure using EffectPartBuilder for consistent building
				local base = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(4, 1, 4)):color(Color3.fromRGB(99, 95, 98)):material(Enum.Material.Concrete):position(options.position):spawn()
				base.Name = "Base"
				base.Anchored = true
				-- Create pillars using core helpers
				local _position = options.position
				local _vector3 = Vector3.new(-1.5, 2, -1.5)
				local _exp_1 = _position + _vector3
				local _position_1 = options.position
				local _vector3_1 = Vector3.new(1.5, 2, -1.5)
				local _exp_2 = _position_1 + _vector3_1
				local _position_2 = options.position
				local _vector3_2 = Vector3.new(-1.5, 2, 1.5)
				local _exp_3 = _position_2 + _vector3_2
				local _position_3 = options.position
				local _vector3_3 = Vector3.new(1.5, 2, 1.5)
				local pillarPositions = { _exp_1, _exp_2, _exp_3, _position_3 + _vector3_3 }
				local model = Instance.new("Model")
				model.Name = "Structure"
				base.Parent = model
				model.PrimaryPart = base
				-- Add pillars with staggered spawn animation
				-- ▼ ReadonlyArray.forEach ▼
				local _callback = function(pillarPos, index)
					task.delay(index * 0.2, function()
						local pillar = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(0.5, 3, 0.5)):color(Color3.fromRGB(120, 115, 118)):material(Enum.Material.Concrete):position(pillarPos):spawn()
						pillar.Name = `Pillar{index + 1}`
						pillar.Anchored = true
						pillar.Parent = model
						-- Animate pillar rising from ground
						pillar.Size = Vector3.new(0.5, 0, 0.5)
						EffectTweenBuilder["for"](EffectTweenBuilder, pillar):duration(0.5):easing(Enum.EasingStyle.Back, Enum.EasingDirection.Out):expand(Vector3.new(0.5, 3, 0.5)):play()
						-- Play construction sound
						playSound("rbxassetid://131961136", 0.2, 0.6, pillar)
					end)
				end
				for _k, _v in pillarPositions do
					_callback(_v, _k - 1, pillarPositions)
				end
				-- ▲ ReadonlyArray.forEach ▲
				-- Create impact flash at base
				createImpactFlash(options.position, 3, 0.4)
				instance = model
				break
			end
			do
				local part = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(2, 2, 2)):color(Color3.fromRGB(128, 128, 128)):position(options.position):spawn()
				instance = part
			end
		until true
		-- Set position and rotation using PositionHelper
		PositionHelper:setPosition(instance, options.position, options.rotation)
		-- Set parent
		instance.Parent = options.parent or game.Workspace
		return instance
	end
	function EntityManager:startHeartbeat()
		self.heartbeatConnection = RunService.Heartbeat:Connect(function()
			-- Update entity positions from their instances using PositionHelper
			local _exp = self.entities
			-- ▼ ReadonlyMap.forEach ▼
			local _callback = function(entity)
				if entity.isActive and entity.instance.Parent then
					entity.position = PositionHelper:getPosition(entity.instance)
				end
			end
			for _k, _v in _exp do
				_callback(_v, _k, _exp)
			end
			-- ▲ ReadonlyMap.forEach ▲
		end)
	end
end
return {
	EntityManager = EntityManager,
}
