import { RunService, Players, ReplicatedStorage } from "@rbxts/services";
import { Debug<PERSON>enderer } from "./DebugRenderer";

export interface PerformanceData {
	fps: number;
	frameTime: number;
	memoryUsage: number;
	networkReceive: number;
	networkSend: number;
	instanceCount: number;
	heartbeatTime: number;
}

export class PerformanceMonitor {
	private renderer: DebugRenderer;
	private performanceLabel?: TextLabel;
	private fpsHistory: number[] = [];
	private frameTimeHistory: number[] = [];
	private lastUpdateTime = 0;
	private updateInterval = 0.5; // Update every 0.5 seconds
	private maxHistorySize = 60; // Keep 60 samples (30 seconds at 0.5s intervals)

	constructor(renderer: DebugRenderer) {
		this.renderer = renderer;
		this.setupGUI();
	}

	public update(deltaTime: number): void {
		const currentTime = tick();
		
		// Only update at specified intervals
		if (currentTime - this.lastUpdateTime < this.updateInterval) return;
		
		this.lastUpdateTime = currentTime;
		
		const perfData = this.collectPerformanceData(deltaTime);
		this.updateHistory(perfData);
		this.updatePerformanceDisplay(perfData);
	}

	private setupGUI(): void {
		this.performanceLabel = this.renderer.createGUIElement(
			"Performance",
			UDim2.fromScale(1, 1).sub(UDim2.fromOffset(335, 825)),
			UDim2.fromOffset(320, 180),
			"Performance Monitor"
		);
	}

	private collectPerformanceData(deltaTime: number): PerformanceData {
		// Calculate FPS
		const fps = math.floor(1 / deltaTime);
		
		// Get frame time in milliseconds
		const frameTime = deltaTime * 1000;
		
		// Get memory usage (approximation)
		const memoryUsage = this.getMemoryUsage();
		
		// Get network stats (if available)
		const networkStats = this.getNetworkStats();
		
		// Get instance count
		const instanceCount = this.getInstanceCount();
		
		// Get heartbeat time
		const heartbeatTime = this.getHeartbeatTime();

		return {
			fps,
			frameTime,
			memoryUsage,
			networkReceive: networkStats.receive,
			networkSend: networkStats.send,
			instanceCount,
			heartbeatTime
		};
	}

	private updateHistory(perfData: PerformanceData): void {
		// Update FPS history
		this.fpsHistory.push(perfData.fps);
		if (this.fpsHistory.size() > this.maxHistorySize) {
			this.fpsHistory.shift();
		}

		// Update frame time history
		this.frameTimeHistory.push(perfData.frameTime);
		if (this.frameTimeHistory.size() > this.maxHistorySize) {
			this.frameTimeHistory.shift();
		}
	}

	private updatePerformanceDisplay(perfData: PerformanceData): void {
		if (!this.performanceLabel) return;

		const avgFPS = this.calculateAverage(this.fpsHistory);
		const minFPS = this.fpsHistory.size() > 0 ? math.min(...this.fpsHistory) : 0;
		const maxFPS = this.fpsHistory.size() > 0 ? math.max(...this.fpsHistory) : 0;

		const avgFrameTime = this.calculateAverage(this.frameTimeHistory);
		const minFrameTime = this.frameTimeHistory.size() > 0 ? math.min(...this.frameTimeHistory) : 0;
		const maxFrameTime = this.frameTimeHistory.size() > 0 ? math.max(...this.frameTimeHistory) : 0;

		const info = [
			"=== PERFORMANCE MONITOR ===",
			"",
			"FRAME RATE:",
			`  Current: ${perfData.fps} FPS`,
			`  Average: ${math.floor(avgFPS)} FPS`,
			`  Min/Max: ${minFPS}/${maxFPS} FPS`,
			"",
			"FRAME TIME:",
			`  Current: ${math.floor(perfData.frameTime * 100) / 100} ms`,
			`  Average: ${math.floor(avgFrameTime * 100) / 100} ms`,
			`  Min/Max: ${math.floor(minFrameTime * 100) / 100}/${math.floor(maxFrameTime * 100) / 100} ms`,
			"",
			"MEMORY:",
			`  Usage: ${math.floor(perfData.memoryUsage * 10) / 10} MB`,
			"",
			"NETWORK:",
			`  Receive: ${math.floor(perfData.networkReceive * 10) / 10} KB/s`,
			`  Send: ${math.floor(perfData.networkSend * 10) / 10} KB/s`,
			"",
			"SYSTEM:",
			`  Instances: ${perfData.instanceCount}`,
			`  Heartbeat: ${math.floor(perfData.heartbeatTime * 100) / 100} ms`,
			"",
			this.getPerformanceRating(avgFPS)
		];

		this.performanceLabel.Text = info.join("\n");
	}

	private getMemoryUsage(): number {
		// This is an approximation - Roblox doesn't expose direct memory usage
		// We can estimate based on instance count and other factors
		try {
			// Stats is not a function in Roblox, it's a service
			// For now, we'll use a simple estimation
		} catch {
			// Fallback estimation
		}

		// Rough estimation based on instance count
		const instanceCount = this.getInstanceCount();
		return instanceCount * 0.001; // Very rough estimate: 1KB per instance
	}

	private getNetworkStats(): { receive: number; send: number } {
		// Placeholder - actual network stats would require more complex implementation
		return { receive: 0, send: 0 };
	}

	private getInstanceCount(): number {
		let count = 0;
		
		const countInstances = (parent: Instance) => {
			count++;
			parent.GetChildren().forEach(child => {
				countInstances(child);
			});
		};

		try {
			countInstances(game.Workspace);
			countInstances(Players);
			countInstances(ReplicatedStorage);
		} catch {
			// If we can't access these, return 0
			return 0;
		}

		return count;
	}

	private getHeartbeatTime(): number {
		// Measure time for a single heartbeat step
		const startTime = tick();
		RunService.Heartbeat.Wait();
		return (tick() - startTime) * 1000; // Convert to milliseconds
	}

	private calculateAverage(numbers: number[]): number {
		if (numbers.size() === 0) return 0;
		const sum = numbers.reduce((acc, num) => acc + num, 0);
		return sum / numbers.size();
	}

	private getPerformanceRating(avgFPS: number): string {
		if (avgFPS >= 55) {
			return "Performance: EXCELLENT ✅";
		} else if (avgFPS >= 45) {
			return "Performance: GOOD ✅";
		} else if (avgFPS >= 30) {
			return "Performance: FAIR ⚠️";
		} else if (avgFPS >= 20) {
			return "Performance: POOR ⚠️";
		} else {
			return "Performance: CRITICAL ❌";
		}
	}

	// Method to get performance summary for external use
	public getPerformanceSummary(): string {
		if (this.fpsHistory.size() === 0) return "No data";

		const avgFPS = this.calculateAverage(this.fpsHistory);
		const avgFrameTime = this.calculateAverage(this.frameTimeHistory);

		return `FPS: ${math.floor(avgFPS)} | Frame: ${math.floor(avgFrameTime * 10) / 10}ms`;
	}

	// Method to check if performance is acceptable
	public isPerformanceGood(): boolean {
		if (this.fpsHistory.size() === 0) return true;
		
		const avgFPS = this.calculateAverage(this.fpsHistory);
		return avgFPS >= 30; // Consider 30+ FPS as acceptable
	}

	// Method to get detailed performance report
	public getDetailedReport(): PerformanceData | undefined {
		if (this.fpsHistory.size() === 0) return undefined;

		return {
			fps: this.fpsHistory[this.fpsHistory.size() - 1],
			frameTime: this.frameTimeHistory[this.frameTimeHistory.size() - 1],
			memoryUsage: this.getMemoryUsage(),
			networkReceive: 0,
			networkSend: 0,
			instanceCount: this.getInstanceCount(),
			heartbeatTime: this.getHeartbeatTime()
		};
	}

	public cleanup(): void {
		this.fpsHistory = [];
		this.frameTimeHistory = [];
	}
}
