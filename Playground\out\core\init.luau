-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "grid", "Grid") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "input", "Input") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "modal", "Modal") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "list", "ListView") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "image", "Image") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "AutoDockFrame") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "useZIndex") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "actionbar") or {} do
	exports[_k] = _v
end
-- Effects
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "FrameAnimationHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "ParticleHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "SoundHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "VisualEffectUtils") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "TrailHelper") or {} do
	exports[_k] = _v
end
-- Helpers
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper") or {} do
	exports[_k] = _v
end
-- Animations
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "AnimationBuilder") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "CharacterJointManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "LimbAnimator") or {} do
	exports[_k] = _v
end
-- Character Building
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "character", "CharacterBuilder") or {} do
	exports[_k] = _v
end
-- Data Persistence
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "DataStoreHelper") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "PlayerDataManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "DataStore") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "RemoteEventTypes") or {} do
	exports[_k] = _v
end
-- World Systems
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "world") or {} do
	exports[_k] = _v
end
-- Entity Management
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "interfaces", "Entity") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "enums", "EntityType") or {} do
	exports[_k] = _v
end
-- AI System
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "AIController") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "interfaces", "AIBehavior") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "BasicBehaviors") or {} do
	exports[_k] = _v
end
-- Debug System
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug") or {} do
	exports[_k] = _v
end
return exports
