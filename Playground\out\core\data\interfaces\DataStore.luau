-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Re-export the separated interfaces for backward compatibility
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "PlayerData") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "GlobalData") or {} do
	exports[_k] = _v
end
for _k, _v in TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "interfaces", "DataStoreConfig") or {} do
	exports[_k] = _v
end
return exports
