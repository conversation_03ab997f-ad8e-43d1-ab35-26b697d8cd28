-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local ContainerFrame = _core.ContainerFrame
local VerticalFrame = _core.VerticalFrame
local Button = _core.Button
local Label = _core.Label
local ScrollingFrame = _core.ScrollingFrame
local IconButton = _core.IconButton
local useZIndex = _core.useZIndex
local DebugManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug").DebugManager
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local SIZES = _design.SIZES
local function DebugPanel(props)
	local debugEnabled, setDebugEnabled = React.useState(false)
	local aiDebugEnabled, setAiDebugEnabled = React.useState(false)
	local playerDebugEnabled, setPlayerDebugEnabled = React.useState(false)
	local performanceEnabled, setPerformanceEnabled = React.useState(false)
	local pathsEnabled, setPathsEnabled = React.useState(false)
	local visionEnabled, setVisionEnabled = React.useState(false)
	local debugManager = DebugManager:getInstance()
	-- Automatic Z-Index management - this will appear above other panels
	local _binding = useZIndex("debug-panel", true)
	local zIndex = _binding.zIndex
	local toggleMainDebug = function()
		local newState = not debugEnabled
		setDebugEnabled(newState)
		-- Always toggle the debug manager to match our state
		if newState and not debugManager:isEnabled() then
			debugManager:toggle()
		elseif not newState and debugManager:isEnabled() then
			debugManager:toggle()
		end
		print(`🔧 Debug overlay {if newState then "ENABLED" else "DISABLED"}`)
	end
	local toggleAIDebug = function()
		local newState = not aiDebugEnabled
		setAiDebugEnabled(newState)
		debugManager:setConfig({
			showAI = newState,
		})
	end
	local togglePlayerDebug = function()
		local newState = not playerDebugEnabled
		setPlayerDebugEnabled(newState)
		debugManager:setConfig({
			showPlayers = newState,
		})
	end
	local togglePerformance = function()
		local newState = not performanceEnabled
		setPerformanceEnabled(newState)
		debugManager:setConfig({
			showPerformance = newState,
		})
	end
	local togglePaths = function()
		local newState = not pathsEnabled
		setPathsEnabled(newState)
		debugManager:setConfig({
			showPaths = newState,
		})
	end
	local toggleVision = function()
		local newState = not visionEnabled
		setVisionEnabled(newState)
		debugManager:setConfig({
			showVision = newState,
		})
	end
	local runDebugDemo = function()
		-- Make sure debug overlay is enabled first
		if not debugEnabled then
			print("❌ Enable Debug Overlay first!")
			return nil
		end
		local Players = game:GetService("Players")
		local localPlayer = Players.LocalPlayer
		if localPlayer and localPlayer.Character and localPlayer.Character.PrimaryPart then
			local playerPos = localPlayer.Character.PrimaryPart.Position
			print("🎨 Running debug demo...")
			-- Draw coordinate axes
			local _vector3 = Vector3.new(10, 0, 0)
			debugManager:drawLine(playerPos, playerPos + _vector3, Color3.fromRGB(255, 0, 0), 8)
			local _vector3_1 = Vector3.new(0, 10, 0)
			debugManager:drawLine(playerPos, playerPos + _vector3_1, Color3.fromRGB(0, 255, 0), 8)
			local _vector3_2 = Vector3.new(0, 0, 10)
			debugManager:drawLine(playerPos, playerPos + _vector3_2, Color3.fromRGB(0, 0, 255), 8)
			-- Draw some spheres
			local _vector3_3 = Vector3.new(5, 5, 5)
			debugManager:drawSphere(playerPos + _vector3_3, 2, Color3.fromRGB(255, 255, 0), 8)
			local _vector3_4 = Vector3.new(-5, 5, -5)
			debugManager:drawSphere(playerPos + _vector3_4, 1.5, Color3.fromRGB(255, 0, 255), 8)
			local _vector3_5 = Vector3.new(0, 0, 0)
			debugManager:drawSphere(playerPos + _vector3_5, 0.5, Color3.fromRGB(255, 255, 255), 8)
			-- Draw some text
			local _vector3_6 = Vector3.new(0, 12, 0)
			debugManager:drawText(playerPos + _vector3_6, "Debug Demo Active!", Color3.fromRGB(255, 255, 255), 8)
			local _vector3_7 = Vector3.new(10, 2, 0)
			debugManager:drawText(playerPos + _vector3_7, "X-Axis", Color3.fromRGB(255, 0, 0), 8)
			local _vector3_8 = Vector3.new(0, 10, 2)
			debugManager:drawText(playerPos + _vector3_8, "Y-Axis", Color3.fromRGB(0, 255, 0), 8)
			local _vector3_9 = Vector3.new(2, 2, 10)
			debugManager:drawText(playerPos + _vector3_9, "Z-Axis", Color3.fromRGB(0, 0, 255), 8)
			-- Log debug messages
			debugManager:logDebug("Demo", "Debug system demonstration started", {
				position = playerPos,
			})
			debugManager:logDebug("Demo", "Drawing coordinate axes and markers")
			print(`🔍 Debug demo running! Visual elements will appear for 8 seconds`)
			print(`🎯 Look around your character to see the debug visuals`)
		else
			print("❌ Could not get player position for demo")
		end
	end
	-- Update state when panel opens
	React.useEffect(function()
		if props.isOpen then
			local config = debugManager:getConfig()
			setDebugEnabled(debugManager:isEnabled())
			setAiDebugEnabled(config.showAI)
			setPlayerDebugEnabled(config.showPlayers)
			setPerformanceEnabled(config.showPerformance)
			setPathsEnabled(config.showPaths)
			setVisionEnabled(config.showVision)
		end
	end, { props.isOpen })
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	return React.createElement(React.Fragment, nil, React.createElement(ContainerFrame, {
		size = UDim2.new(0, 600, 0, 500),
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		backgroundColor = COLORS.bg.base,
		backgroundTransparency = 0,
		borderThickness = 1,
		borderColor = COLORS.border.l2,
		cornerRadius = BORDER_RADIUS.md,
		zIndex = zIndex,
	}, React.createElement(ContainerFrame, {
		size = UDim2.new(1, 0, 0, 60),
		position = UDim2.new(0, 0, 0, 0),
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		borderThickness = 0,
		padding = SIZES.padding,
	}, React.createElement(Label, {
		text = "🔧 Debug System Controls",
		fontSize = SIZES.fontSize + 4,
		textColor = COLORS.text.main,
		bold = true,
		position = UDim2.new(0, 0, 0.5, 0),
		anchorPoint = Vector2.new(0, 0.5),
		size = UDim2.new(1, -50, 0, 20),
	}), React.createElement(IconButton, {
		icon = "✕",
		onClick = props.onClose,
		size = UDim2.new(0, 32, 0, 32),
		position = UDim2.new(1, -16, 0.5, 0),
		anchorPoint = Vector2.new(1, 0.5),
	})), React.createElement(ScrollingFrame, {
		size = UDim2.new(1, 0, 1, -60),
		position = UDim2.new(0, 0, 0, 60),
		backgroundTransparency = 1,
		borderThickness = 0,
		scrollingDirection = Enum.ScrollingDirection.Y,
		automaticCanvasSize = Enum.AutomaticSize.Y,
		scrollBarThickness = 8,
	}, React.createElement(VerticalFrame, {
		spacing = 12,
		padding = 16,
		fitContent = true,
	}, React.createElement(ContainerFrame, {
		padding = SIZES.padding + 2,
		layoutOrder = 1,
		fitContent = true,
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		cornerRadius = BORDER_RADIUS.md,
		borderColor = COLORS.border.l2,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
		fitContent = true,
	}, React.createElement(Label, {
		text = "🔧 Main Debug Control",
		fontSize = SIZES.fontSize + 6,
		textColor = COLORS.text.main,
		bold = true,
		autoSize = true,
	}), React.createElement(Label, {
		text = "Enable the debug overlay to see visual debugging information in-game.",
		fontSize = SIZES.fontSize + 2,
		textColor = COLORS.text.secondary,
		textWrapped = true,
		autoSize = true,
	}), React.createElement(Button, {
		text = if debugEnabled then "🟢 Debug Overlay: ENABLED" else "🔴 Debug Overlay: DISABLED",
		onClick = toggleMainDebug,
	}))), React.createElement(ContainerFrame, {
		padding = SIZES.padding + 2,
		layoutOrder = 2,
		fitContent = true,
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		cornerRadius = BORDER_RADIUS.md,
		borderColor = COLORS.border.l2,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
		fitContent = true,
	}, React.createElement(Label, {
		text = "🎛️ Debug Features",
		fontSize = SIZES.fontSize + 6,
		textColor = COLORS.text.main,
		bold = true,
		autoSize = true,
	}), React.createElement(Label, {
		text = "Toggle individual debug features. Debug overlay must be enabled first.",
		fontSize = SIZES.fontSize + 2,
		textColor = COLORS.text.secondary,
		textWrapped = true,
		autoSize = true,
	}), React.createElement(VerticalFrame, {
		spacing = 6,
		padding = 0,
		fitContent = true,
	}, React.createElement(Button, {
		text = if aiDebugEnabled then "🤖 AI Debug: ON" else "🤖 AI Debug: OFF",
		onClick = toggleAIDebug,
	}), React.createElement(Button, {
		text = if playerDebugEnabled then "👤 Player Debug: ON" else "👤 Player Debug: OFF",
		onClick = togglePlayerDebug,
	}), React.createElement(Button, {
		text = if performanceEnabled then "⚡ Performance Monitor: ON" else "⚡ Performance Monitor: OFF",
		onClick = togglePerformance,
	}), React.createElement(Button, {
		text = if pathsEnabled then "🛤️ Path Visualization: ON" else "🛤️ Path Visualization: OFF",
		onClick = togglePaths,
	}), React.createElement(Button, {
		text = if visionEnabled then "👁️ Vision Cones: ON" else "👁️ Vision Cones: OFF",
		onClick = toggleVision,
	})))), React.createElement(ContainerFrame, {
		padding = SIZES.padding + 2,
		layoutOrder = 3,
		fitContent = true,
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		cornerRadius = BORDER_RADIUS.md,
		borderColor = COLORS.border.l2,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
		fitContent = true,
	}, React.createElement(Label, {
		text = "🎨 Debug Demo",
		fontSize = SIZES.fontSize + 6,
		textColor = COLORS.text.main,
		bold = true,
		autoSize = true,
	}), React.createElement(Label, {
		text = "Run a visual demonstration of the debug system with colored lines, spheres, and text.",
		fontSize = SIZES.fontSize + 2,
		textColor = COLORS.text.secondary,
		textWrapped = true,
		autoSize = true,
	}), React.createElement(Button, {
		text = "🎨 Run Visual Demo",
		onClick = runDebugDemo,
	}))), React.createElement(ContainerFrame, {
		padding = SIZES.padding + 2,
		layoutOrder = 4,
		fitContent = true,
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		cornerRadius = BORDER_RADIUS.md,
		borderColor = COLORS.border.l2,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 6,
		padding = 0,
		fitContent = true,
	}, React.createElement(Label, {
		text = "📖 How to Use",
		fontSize = SIZES.fontSize + 4,
		textColor = COLORS.text.main,
		bold = true,
		autoSize = true,
	}), React.createElement(VerticalFrame, {
		spacing = 4,
		padding = 0,
		fitContent = true,
	}, React.createElement(Label, {
		text = "1. Enable Debug Overlay first • 2. Toggle features • 3. Spawn AI NPCs in World panel",
		fontSize = SIZES.fontSize + 1,
		textColor = COLORS.text.secondary,
		textWrapped = true,
		autoSize = true,
	}), React.createElement(Label, {
		text = "4. Run visual demo • 5. Performance monitor shows FPS and memory stats",
		fontSize = SIZES.fontSize + 1,
		textColor = COLORS.text.secondary,
		textWrapped = true,
		autoSize = true,
	}))))))))
end
return {
	DebugPanel = DebugPanel,
}
