-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local DataStoreHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "data", "DataStoreHelper").DataStoreHelper
local PlayerDataManager
do
	PlayerDataManager = setmetatable({}, {
		__tostring = function()
			return "PlayerDataManager"
		end,
	})
	PlayerDataManager.__index = PlayerDataManager
	function PlayerDataManager.new(...)
		local self = setmetatable({}, PlayerDataManager)
		return self:constructor(...) or self
	end
	function PlayerDataManager:constructor()
		self.loadedPlayers = {}
		self.dataStore = DataStoreHelper:getInstance()
		self:setupPlayerEvents()
	end
	function PlayerDataManager:getInstance()
		if not PlayerDataManager.instance then
			PlayerDataManager.instance = PlayerDataManager.new()
		end
		return PlayerDataManager.instance
	end
	PlayerDataManager.onPlayerJoin = TS.async(function(self, player)
		print(`Loading data for player: {player.Name} ({player.UserId})`)
		local result = TS.await(self.dataStore:loadPlayerData(player.UserId))
		if result.success and result.data then
			-- Update last login time and session count
			local _object = table.clone(result.data)
			setmetatable(_object, nil)
			_object.lastLogin = tick()
			local _left = "metadata"
			local _object_1 = table.clone(result.data.metadata)
			setmetatable(_object_1, nil)
			_object_1.totalSessions = result.data.metadata.totalSessions + 1
			_object[_left] = _object_1
			local updatedData = _object
			TS.await(self.dataStore:savePlayerData(updatedData))
			local _loadedPlayers = self.loadedPlayers
			local _userId = player.UserId
			_loadedPlayers[_userId] = true
			print(`✅ Successfully loaded data for {player.Name}`)
			return {
				success = true,
				data = updatedData,
			}
		else
			print(`❌ Failed to load data for {player.Name}: {result.error}`)
			return result
		end
	end)
	PlayerDataManager.onPlayerLeave = TS.async(function(self, player)
		local _loadedPlayers = self.loadedPlayers
		local _userId = player.UserId
		if _loadedPlayers[_userId] ~= nil then
			print(`Saving data for leaving player: {player.Name}`)
			local result = TS.await(self.dataStore:loadPlayerData(player.UserId))
			if result.success and result.data then
				-- Update playtime before saving
				local sessionTime = tick() - result.data.lastLogin
				local _object = table.clone(result.data)
				setmetatable(_object, nil)
				_object.playtime = result.data.playtime + sessionTime
				local updatedData = _object
				local saveResult = TS.await(self.dataStore:savePlayerData(updatedData))
				if saveResult.success then
					print(`✅ Successfully saved data for {player.Name}`)
				else
					print(`❌ Failed to save data for {player.Name}: {saveResult.error}`)
				end
			end
			local _loadedPlayers_1 = self.loadedPlayers
			local _userId_1 = player.UserId
			_loadedPlayers_1[_userId_1] = nil
		end
	end)
	PlayerDataManager.updatePlayerGameData = TS.async(function(self, userId, gameDataUpdates)
		return self.dataStore:updatePlayerGameData(userId, gameDataUpdates)
	end)
	PlayerDataManager.updatePlayerSettings = TS.async(function(self, userId, settings)
		local result = TS.await(self.dataStore:loadPlayerData(userId))
		if not result.success or not result.data then
			return {
				success = false,
				error = "Failed to load player data",
			}
		end
		local _object = table.clone(result.data.settings)
		setmetatable(_object, nil)
		for _k, _v in settings do
			_object[_k] = _v
		end
		local newSettings = _object
		local updateResult = TS.await(self.dataStore:updatePlayerData(userId, {
			settings = newSettings,
		}))
		if updateResult.success then
			print(`⚙️ Updated settings for player {userId}`)
			return {
				success = true,
				data = newSettings,
			}
		else
			return {
				success = false,
				error = updateResult.error,
			}
		end
	end)
	function PlayerDataManager:isPlayerLoaded(userId)
		local _loadedPlayers = self.loadedPlayers
		local _userId = userId
		return _loadedPlayers[_userId] ~= nil
	end
	function PlayerDataManager:getLoadedPlayerCount()
		-- ▼ ReadonlySet.size ▼
		local _size = 0
		for _ in self.loadedPlayers do
			_size += 1
		end
		-- ▲ ReadonlySet.size ▲
		return _size
	end
	function PlayerDataManager:setupPlayerEvents()
		Players.PlayerAdded:Connect(function(player)
			self:onPlayerJoin(player)
		end)
		Players.PlayerRemoving:Connect(function(player)
			self:onPlayerLeave(player)
		end)
	end
	function PlayerDataManager:cleanup()
		table.clear(self.loadedPlayers)
		self.dataStore:cleanup()
	end
end
return {
	PlayerDataManager = PlayerDataManager,
}
