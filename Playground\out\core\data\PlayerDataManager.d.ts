import { BasePlayerData, PlayerSettings } from "./interfaces/PlayerData";
import { DataStoreResult } from "./interfaces/DataStoreConfig";
export declare class PlayerDataManager<TPlayerData extends BasePlayerData = BasePlayerData> {
    private static instance;
    private dataStore;
    private loadedPlayers;
    private constructor();
    static getInstance<T extends BasePlayerData = BasePlayerData>(): PlayerDataManager<T>;
    onPlayerJoin(player: Player): Promise<DataStoreResult<TPlayerData>>;
    onPlayerLeave(player: Player): Promise<void>;
    updatePlayerGameData(userId: number, gameDataUpdates: Record<string, unknown>): Promise<DataStoreResult<TPlayerData>>;
    updatePlayerSettings(userId: number, settings: Partial<PlayerSettings>): Promise<DataStoreResult<PlayerSettings>>;
    isPlayerLoaded(userId: number): boolean;
    getLoadedPlayerCount(): number;
    private setupPlayerEvents;
    cleanup(): void;
}
