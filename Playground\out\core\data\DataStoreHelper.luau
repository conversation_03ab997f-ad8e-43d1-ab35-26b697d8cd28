-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local DataStoreService = _services.DataStoreService
local Players = _services.Players
local RunService = _services.RunService
local DataStoreHelper
do
	DataStoreHelper = setmetatable({}, {
		__tostring = function()
			return "DataStoreHelper"
		end,
	})
	DataStoreHelper.__index = DataStoreHelper
	function DataStoreHelper.new(...)
		local self = setmetatable({}, DataStoreHelper)
		return self:constructor(...) or self
	end
	function DataStoreHelper:constructor()
		self.cache = {}
		self.options = {
			retryAttempts = 2,
			retryDelay = 2,
			cacheTimeout = 300,
			autoSave = false,
			autoSaveInterval = 300,
			enableStudioTesting = false,
		}
		-- Check if we're in Studio
		local isStudio = game:GetService("RunService"):IsStudio()
		if isStudio then
			-- Studio mode - use mock mode by default
			print("📝 Studio mode detected - Using mock DataStore (no persistence)")
			print("💡 To enable real DataStore testing, call dataStore.enableStudioTesting()")
		else
			-- Production mode - try to initialize DataStores
			TS.try(function()
				self.playerDataStore = DataStoreService:GetDataStore("PlayerData")
				self.globalDataStore = DataStoreService:GetDataStore("GlobalData")
				print("✅ DataStore initialized for production")
			end, function(error)
				print(`❌ DataStore initialization failed: {error}`)
				print("📝 Falling back to mock mode")
			end)
		end
		if self.options.autoSave then
			self:startAutoSave()
		end
	end
	function DataStoreHelper:getInstance()
		if not DataStoreHelper.instance then
			DataStoreHelper.instance = DataStoreHelper.new()
		end
		return DataStoreHelper.instance
	end
	function DataStoreHelper:configure(options)
		local _object = table.clone(self.options)
		setmetatable(_object, nil)
		for _k, _v in options do
			_object[_k] = _v
		end
		self.options = _object
		if self.autoSaveConnection then
			self.autoSaveConnection:Disconnect()
		end
		if self.options.autoSave then
			self:startAutoSave()
		end
	end
	function DataStoreHelper:enableStudioTesting()
		if not game:GetService("RunService"):IsStudio() then
			print("⚠️ enableStudioTesting() only works in Studio")
			return nil
		end
		TS.try(function()
			self.playerDataStore = DataStoreService:GetDataStore("PlayerData")
			self.globalDataStore = DataStoreService:GetDataStore("GlobalData")
			self.options.enableStudioTesting = true
			print("✅ Studio DataStore testing enabled!")
			print("💡 Make sure 'Studio Access to API Services' is enabled in Game Settings")
		end, function(error)
			print(`❌ Failed to enable Studio DataStore testing: {error}`)
			print("💡 Please enable 'Studio Access to API Services' in Game Settings → Security")
		end)
	end
	function DataStoreHelper:disableAutoSave()
		if self.autoSaveConnection then
			self.autoSaveConnection:Disconnect()
			self.autoSaveConnection = nil
		end
		self.options.autoSave = false
		print("🛑 Auto-save disabled")
	end
	DataStoreHelper.loadPlayerData = TS.async(function(self, userId)
		local cacheKey = `player_{userId}`
		-- Check cache first
		local cached = self:getFromCache(cacheKey)
		if cached then
			return {
				success = true,
				data = cached,
				fromCache = true,
			}
		end
		-- If DataStore is not available, return default data
		if not self.playerDataStore then
			local defaultData = self:createDefaultPlayerData(userId)
			self:setCache(cacheKey, defaultData)
			return {
				success = true,
				data = defaultData,
				fromCache = false,
			}
		end
		-- Load from DataStore with retry logic
		return self:retryOperation(function()
			return TS.Promise.new(function(resolve)
				TS.try(function()
					local data = self.playerDataStore:GetAsync(`Player_{userId}`)
					local playerData = if data ~= 0 and data == data and data ~= "" and data then data else self:createDefaultPlayerData(userId)
					-- Cache the result
					self:setCache(cacheKey, playerData)
					resolve({
						success = true,
						data = playerData,
					})
				end, function(error)
					error(error)
				end)
			end)
		end)
	end)
	DataStoreHelper.savePlayerData = TS.async(function(self, playerData)
		local cacheKey = `player_{playerData.userId}`
		-- Update cache
		self:setCache(cacheKey, playerData)
		-- If DataStore is not available, just return success (mock mode)
		if not self.playerDataStore then
			-- Reduce spam - only log occasionally
			if math.random() < 0.1 then
				print(`📝 Mock mode: Player data cached (not persisted)`)
			end
			return {
				success = true,
				data = true,
			}
		end
		-- Save to DataStore with retry logic
		return self:retryOperation(function()
			return TS.Promise.new(function(resolve)
				TS.try(function()
					self.playerDataStore:SetAsync(`Player_{playerData.userId}`, playerData)
					resolve({
						success = true,
						data = true,
					})
				end, function(error)
					error(error)
				end)
			end)
		end)
	end)
	DataStoreHelper.updatePlayerData = TS.async(function(self, userId, updates)
		local loadResult = TS.await(self:loadPlayerData(userId))
		if not loadResult.success or not loadResult.data then
			return {
				success = false,
				error = "Failed to load player data",
			}
		end
		local _object = table.clone(loadResult.data)
		setmetatable(_object, nil)
		for _k, _v in updates do
			_object[_k] = _v
		end
		local updatedData = _object
		local saveResult = TS.await(self:savePlayerData(updatedData))
		if saveResult.success then
			return {
				success = true,
				data = updatedData,
			}
		else
			return {
				success = false,
				error = saveResult.error,
			}
		end
	end)
	DataStoreHelper.loadGlobalData = TS.async(function(self)
		local cacheKey = "global_data"
		-- Check cache first
		local cached = self:getFromCache(cacheKey)
		if cached then
			return {
				success = true,
				data = cached,
				fromCache = true,
			}
		end
		-- If DataStore is not available, return default data
		if not self.globalDataStore then
			local defaultData = self:createDefaultGlobalData()
			self:setCache(cacheKey, defaultData)
			return {
				success = true,
				data = defaultData,
				fromCache = false,
			}
		end
		-- Load from DataStore with retry logic
		return self:retryOperation(function()
			return TS.Promise.new(function(resolve)
				TS.try(function()
					local data = self.globalDataStore:GetAsync("GlobalData")
					local globalData = if data ~= 0 and data == data and data ~= "" and data then data else self:createDefaultGlobalData()
					-- Cache the result
					self:setCache(cacheKey, globalData)
					resolve({
						success = true,
						data = globalData,
					})
				end, function(error)
					error(error)
				end)
			end)
		end)
	end)
	DataStoreHelper.saveGlobalData = TS.async(function(self, globalData)
		local cacheKey = "global_data"
		-- Update cache
		self:setCache(cacheKey, globalData)
		-- If DataStore is not available, just return success (mock mode)
		if not self.globalDataStore then
			-- Reduce spam - only log occasionally
			if math.random() < 0.05 then
				print(`📝 Mock mode: Global data cached (not persisted)`)
			end
			return {
				success = true,
				data = true,
			}
		end
		-- Save to DataStore with retry logic
		return self:retryOperation(function()
			return TS.Promise.new(function(resolve)
				TS.try(function()
					self.globalDataStore:SetAsync("GlobalData", globalData)
					resolve({
						success = true,
						data = true,
					})
				end, function(error)
					error(error)
				end)
			end)
		end)
	end)
	DataStoreHelper.updatePlayerGameData = TS.async(function(self, userId, gameDataUpdates)
		local loadResult = TS.await(self:loadPlayerData(userId))
		if not loadResult.success or not loadResult.data then
			return {
				success = false,
				error = "Failed to load player data",
			}
		end
		local _object = table.clone(loadResult.data.gameData)
		setmetatable(_object, nil)
		for _k, _v in gameDataUpdates do
			_object[_k] = _v
		end
		local updatedGameData = _object
		local updateResult = TS.await(self:updatePlayerData(userId, {
			gameData = updatedGameData,
		}))
		return updateResult
	end)
	DataStoreHelper.updateGlobalGameData = TS.async(function(self, gameDataUpdates)
		local loadResult = TS.await(self:loadGlobalData())
		if not loadResult.success or not loadResult.data then
			return {
				success = false,
				error = "Failed to load global data",
			}
		end
		local _object = table.clone(loadResult.data.gameData)
		setmetatable(_object, nil)
		for _k, _v in gameDataUpdates do
			_object[_k] = _v
		end
		local updatedGameData = _object
		local _object_1 = table.clone(loadResult.data)
		setmetatable(_object_1, nil)
		_object_1.gameData = updatedGameData
		local updatedGlobalData = _object_1
		local saveResult = TS.await(self:saveGlobalData(updatedGlobalData))
		if saveResult.success then
			return {
				success = true,
				data = updatedGlobalData,
			}
		else
			return {
				success = false,
				error = saveResult.error,
			}
		end
	end)
	function DataStoreHelper:clearCache()
		table.clear(self.cache)
	end
	function DataStoreHelper:getCacheSize()
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.cache do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		return _size
	end
	function DataStoreHelper:cleanup()
		if self.autoSaveConnection then
			self.autoSaveConnection:Disconnect()
		end
		self:clearCache()
	end
	DataStoreHelper.retryOperation = TS.async(function(self, operation)
		local lastError = ""
		do
			local attempt = 1
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					attempt += 1
				else
					_shouldIncrement = true
				end
				if not (attempt <= self.options.retryAttempts) then
					break
				end
				local _exitType, _returns = TS.try(function()
					return TS.TRY_RETURN, { TS.await(operation()) }
				end, function(error)
					lastError = tostring(error)
					print(`DataStore operation failed (attempt {attempt}/{self.options.retryAttempts}): {lastError}`)
					if attempt < self.options.retryAttempts then
						TS.await(self:wait(self.options.retryDelay * attempt))
					end
				end)
				if _exitType then
					return unpack(_returns)
				end
			end
		end
		return {
			success = false,
			error = `Failed after {self.options.retryAttempts} attempts: {lastError}`,
		}
	end)
	function DataStoreHelper:getFromCache(key)
		local _cache = self.cache
		local _key = key
		local cached = _cache[_key]
		if cached and tick() - cached.timestamp < self.options.cacheTimeout then
			return cached.data
		end
		-- Remove expired cache entry
		if cached then
			local _cache_1 = self.cache
			local _key_1 = key
			_cache_1[_key_1] = nil
		end
		return nil
	end
	function DataStoreHelper:setCache(key, data)
		local _cache = self.cache
		local _key = key
		local _arg1 = {
			data = data,
			timestamp = tick(),
		}
		_cache[_key] = _arg1
	end
	function DataStoreHelper:createDefaultPlayerData(userId)
		local coreData = {
			userId = userId,
			lastLogin = tick(),
			playtime = 0,
			createdAt = tick(),
			version = "1.0.0",
		}
		local _object = table.clone(coreData)
		setmetatable(_object, nil)
		_object.gameData = {}
		_object.settings = {
			musicVolume = 0.8,
			sfxVolume = 0.8,
			language = "en",
		}
		_object.metadata = {
			totalSessions = 1,
			averageSessionTime = 0,
			lastSaveTime = tick(),
			dataVersion = 1,
		}
		local baseData = _object
		return baseData
	end
	function DataStoreHelper:createDefaultGlobalData()
		local coreData = {
			serverVersion = "1.0.0",
			maintenanceMode = false,
			lastUpdated = tick(),
		}
		local _object = table.clone(coreData)
		setmetatable(_object, nil)
		_object.gameData = {}
		_object.serverConfig = {
			maxPlayers = 50,
			autoSaveInterval = 60,
			debugMode = false,
		}
		local baseData = _object
		return baseData
	end
	function DataStoreHelper:startAutoSave()
		self.autoSaveConnection = RunService.Heartbeat:Connect(function()
			task.delay(self.options.autoSaveInterval, function()
				self:performAutoSave()
			end)
		end)
	end
	DataStoreHelper.performAutoSave = TS.async(function(self)
		-- Auto-save all online players
		for _, player in Players:GetPlayers() do
			local cacheKey = `player_{player.UserId}`
			local cached = self.cache[cacheKey]
			if cached then
				local result = TS.await(self:savePlayerData(cached.data))
				if result.success then
					print(`Auto-saved data for player {player.Name}`)
				else
					print(`Auto-save failed for player {player.Name}: {result.error}`)
				end
			end
		end
	end)
	function DataStoreHelper:wait(seconds)
		return TS.Promise.new(function(resolve)
			task.delay(seconds, resolve)
		end)
	end
end
return {
	DataStoreHelper = DataStoreHelper,
}
