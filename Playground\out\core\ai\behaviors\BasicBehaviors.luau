-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local IdleBehavior
do
	IdleBehavior = setmetatable({}, {
		__tostring = function()
			return "IdleBehavior"
		end,
	})
	IdleBehavior.__index = IdleBehavior
	function IdleBehavior.new(...)
		local self = setmetatable({}, IdleBehavior)
		return self:constructor(...) or self
	end
	function IdleBehavior:constructor()
		self.name = "Idle"
		self.priority = 1
	end
	function IdleBehavior:canExecute(_context)
		-- Always can idle, but lowest priority
		return true
	end
	function IdleBehavior:execute(context)
		-- Just stand there and occasionally look around
		local _condition = context.blackboard.idleTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local idleTime = _condition
		local newIdleTime = idleTime + context.deltaTime
		-- Look around every 3 seconds
		if newIdleTime > 3 then
			self:lookAround(context)
			context.blackboard.idleTime = 0
		else
			context.blackboard.idleTime = newIdleTime
		end
		return {
			success = true,
			completed = false,
		}
	end
	function IdleBehavior:onEnter(context)
		context.blackboard.idleTime = 0
		print(`😴 {context.entityId} is now idle`)
	end
	function IdleBehavior:lookAround(context)
		-- Random rotation for looking around
		local randomAngle = math.random() * math.pi * 2
		local lookDirection = Vector3.new(math.cos(randomAngle), 0, math.sin(randomAngle))
		local _position = context.position
		local _arg0 = lookDirection * 10
		local lookPosition = _position + _arg0
		PositionHelper:lookAt(context.entity, lookPosition)
	end
end
local FollowBehavior
do
	FollowBehavior = setmetatable({}, {
		__tostring = function()
			return "FollowBehavior"
		end,
	})
	FollowBehavior.__index = FollowBehavior
	function FollowBehavior.new(...)
		local self = setmetatable({}, FollowBehavior)
		return self:constructor(...) or self
	end
	function FollowBehavior:constructor()
		self.name = "Follow"
		self.priority = 5
	end
	function FollowBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		return distance <= 30 and distance > 5
	end
	function FollowBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		-- Move towards target
		self:moveTowards(context, context.targetPosition)
		-- Look at target
		PositionHelper:lookAt(context.entity, context.targetPosition)
		return {
			success = true,
			completed = distance <= 5,
		}
	end
	function FollowBehavior:onEnter(context)
		print(`🏃 {context.entityId} is following target`)
	end
	function FollowBehavior:moveTowards(context, targetPosition)
		-- Use simple obstacle avoidance
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		-- Use smooth Roblox movement instead of teleportation
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				-- Use Humanoid.MoveTo for natural character movement
				humanoid:MoveTo(clearTarget)
			else
				-- Fallback: smooth CFrame movement for non-humanoid models
				self:smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			-- Smooth movement for single parts
			self:smoothMoveTo(context.entity, clearTarget, context.deltaTime)
		end
	end
	function FollowBehavior:smoothMoveTo(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 16
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			-- Use CFrame for smooth rotation towards movement direction
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
local PatrolBehavior
do
	PatrolBehavior = setmetatable({}, {
		__tostring = function()
			return "PatrolBehavior"
		end,
	})
	PatrolBehavior.__index = PatrolBehavior
	function PatrolBehavior.new(...)
		local self = setmetatable({}, PatrolBehavior)
		return self:constructor(...) or self
	end
	function PatrolBehavior:constructor()
		self.name = "Patrol"
		self.priority = 3
	end
	function PatrolBehavior:canExecute(context)
		-- Patrol if no target nearby
		return not context.target
	end
	function PatrolBehavior:execute(context)
		local patrolPoints = context.blackboard.patrolPoints
		local _condition = context.blackboard.currentPatrolIndex
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local currentPatrolIndex = _condition
		-- Initialize patrol points if not set
		if not patrolPoints then
			patrolPoints = self:generatePatrolPoints(context.position)
			context.blackboard.patrolPoints = patrolPoints
			context.blackboard.currentPatrolIndex = 0
			currentPatrolIndex = 0
		end
		local targetPoint = patrolPoints[currentPatrolIndex + 1]
		local distance = (context.position - targetPoint).Magnitude
		if distance <= 3 then
			-- Reached patrol point, move to next
			currentPatrolIndex = (currentPatrolIndex + 1) % #patrolPoints
			context.blackboard.currentPatrolIndex = currentPatrolIndex
			-- Wait a bit at each patrol point
			context.blackboard.patrolWaitTime = 2
		else
			-- Move towards current patrol point
			self:moveTowards(context, targetPoint)
			PositionHelper:lookAt(context.entity, targetPoint)
		end
		-- Handle waiting at patrol points
		local _condition_1 = context.blackboard.patrolWaitTime
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = 0
		end
		local waitTime = _condition_1
		if waitTime > 0 then
			context.blackboard.patrolWaitTime = waitTime - context.deltaTime
		end
		return {
			success = true,
			completed = false,
		}
	end
	function PatrolBehavior:onEnter(context)
		print(`🚶 {context.entityId} is patrolling`)
	end
	function PatrolBehavior:generatePatrolPoints(centerPosition)
		local points = {}
		local radius = 15
		local numPoints = 4
		for i = 0, numPoints - 1 do
			local angle = (i / numPoints) * math.pi * 2
			local x = centerPosition.X + math.cos(angle) * radius
			local z = centerPosition.Z + math.sin(angle) * radius
			local _vector3 = Vector3.new(x, centerPosition.Y, z)
			table.insert(points, _vector3)
		end
		return points
	end
	function PatrolBehavior:moveTowards(context, targetPosition)
		local _condition = context.blackboard.patrolWaitTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local waitTime = _condition
		if waitTime > 0 then
			return nil
		end
		-- Use simple obstacle avoidance for patrol
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		-- Use smooth Roblox movement for patrol
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(clearTarget)
			else
				self:smoothPatrolMove(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothPatrolMove(context.entity, clearTarget, context.deltaTime)
		end
	end
	function PatrolBehavior:smoothPatrolMove(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 8
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
local InvestigateBehavior
do
	InvestigateBehavior = setmetatable({}, {
		__tostring = function()
			return "InvestigateBehavior"
		end,
	})
	InvestigateBehavior.__index = InvestigateBehavior
	function InvestigateBehavior.new(...)
		local self = setmetatable({}, InvestigateBehavior)
		return self:constructor(...) or self
	end
	function InvestigateBehavior:constructor()
		self.name = "Investigate"
		self.priority = 4
	end
	function InvestigateBehavior:canExecute(context)
		local investigatePosition = context.blackboard.investigatePosition
		return investigatePosition ~= nil
	end
	function InvestigateBehavior:execute(context)
		local investigatePosition = context.blackboard.investigatePosition
		if not investigatePosition then
			return {
				success = false,
				completed = true,
			}
		end
		local distance = (context.position - investigatePosition).Magnitude
		if distance <= 3 then
			-- Reached investigation point
			local _condition = context.blackboard.investigateTime
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 0
			end
			local investigateTime = _condition
			local newInvestigateTime = investigateTime + context.deltaTime
			if newInvestigateTime >= 3 then
				-- Finished investigating
				context.blackboard.investigatePosition = nil
				context.blackboard.investigateTime = 0
				return {
					success = true,
					completed = true,
				}
			else
				-- Look around while investigating
				self:lookAround(context)
				context.blackboard.investigateTime = newInvestigateTime
			end
		else
			-- Move towards investigation point
			self:moveTowards(context, investigatePosition)
			PositionHelper:lookAt(context.entity, investigatePosition)
		end
		return {
			success = true,
			completed = false,
		}
	end
	function InvestigateBehavior:onEnter(context)
		print(`🔍 {context.entityId} is investigating`)
		context.blackboard.investigateTime = 0
	end
	function InvestigateBehavior:moveTowards(context, targetPosition)
		-- Use simple obstacle avoidance for investigation
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		-- Use smooth Roblox movement for investigation
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(clearTarget)
			else
				self:smoothInvestigateMove(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothInvestigateMove(context.entity, clearTarget, context.deltaTime)
		end
	end
	function InvestigateBehavior:smoothInvestigateMove(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 12
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
	function InvestigateBehavior:lookAround(context)
		local time = tick()
		local angle = time * 2
		local lookDirection = Vector3.new(math.cos(angle), 0, math.sin(angle))
		local _position = context.position
		local _arg0 = lookDirection * 10
		local lookPosition = _position + _arg0
		PositionHelper:lookAt(context.entity, lookPosition)
	end
end
local FleeBehavior
do
	FleeBehavior = setmetatable({}, {
		__tostring = function()
			return "FleeBehavior"
		end,
	})
	FleeBehavior.__index = FleeBehavior
	function FleeBehavior.new(...)
		local self = setmetatable({}, FleeBehavior)
		return self:constructor(...) or self
	end
	function FleeBehavior:constructor()
		self.name = "Flee"
		self.priority = 8
	end
	function FleeBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		local shouldFlee = context.blackboard.shouldFlee or false
		return shouldFlee or distance < 8
	end
	function FleeBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		-- Run away from target using smooth movement
		local _position = context.position
		local _targetPosition = context.targetPosition
		local fleeDirection = (_position - _targetPosition).Unit
		local _position_1 = context.position
		local _arg0 = fleeDirection * 10
		local fleeTarget = _position_1 + _arg0
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(fleeTarget)
				humanoid.WalkSpeed = 20
			else
				self:smoothFleeMove(context.entity.PrimaryPart, fleeTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothFleeMove(context.entity, fleeTarget, context.deltaTime)
		end
		-- Check if we've fled far enough
		local _position_2 = context.position
		local _targetPosition_1 = context.targetPosition
		local distance = (_position_2 - _targetPosition_1).Magnitude
		if distance > 25 then
			context.blackboard.shouldFlee = false
			return {
				success = true,
				completed = true,
			}
		end
		return {
			success = true,
			completed = false,
		}
	end
	function FleeBehavior:onEnter(context)
		print(`😱 {context.entityId} is fleeing!`)
	end
	function FleeBehavior:smoothFleeMove(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 20
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
local AttackBehavior
do
	AttackBehavior = setmetatable({}, {
		__tostring = function()
			return "AttackBehavior"
		end,
	})
	AttackBehavior.__index = AttackBehavior
	function AttackBehavior.new(...)
		local self = setmetatable({}, AttackBehavior)
		return self:constructor(...) or self
	end
	function AttackBehavior:constructor()
		self.name = "Attack"
		self.priority = 7
	end
	function AttackBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		return distance <= 8
	end
	function AttackBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		-- Move closer if not in attack range
		if distance > 5 then
			self:moveTowards(context, context.targetPosition)
			PositionHelper:lookAt(context.entity, context.targetPosition)
			return {
				success = true,
				completed = false,
			}
		end
		-- Perform attack
		local _condition = context.blackboard.lastAttackTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local lastAttackTime = _condition
		local currentTime = tick()
		local attackCooldown = 2
		if currentTime - lastAttackTime >= attackCooldown then
			self:performAttack(context)
			context.blackboard.lastAttackTime = currentTime
		end
		-- Look at target while attacking
		PositionHelper:lookAt(context.entity, context.targetPosition)
		return {
			success = true,
			completed = false,
		}
	end
	function AttackBehavior:onEnter(context)
		print(`⚔️ {context.entityId} is attacking!`)
		context.blackboard.lastAttackTime = 0
	end
	function AttackBehavior:moveTowards(context, targetPosition)
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(clearTarget)
				humanoid.WalkSpeed = 18
			else
				self:smoothAttackMove(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothAttackMove(context.entity, clearTarget, context.deltaTime)
		end
	end
	function AttackBehavior:smoothAttackMove(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 18
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
	function AttackBehavior:performAttack(context)
		-- Simple attack effect - you can customize this
		print(`💥 {context.entityId} attacks!`)
		-- Add visual effect at target position
		if context.targetPosition then
			-- You could spawn an effect here using your EffectPartBuilder
			-- For now, just print the attack
		end
	end
end
local WanderBehavior
do
	WanderBehavior = setmetatable({}, {
		__tostring = function()
			return "WanderBehavior"
		end,
	})
	WanderBehavior.__index = WanderBehavior
	function WanderBehavior.new(...)
		local self = setmetatable({}, WanderBehavior)
		return self:constructor(...) or self
	end
	function WanderBehavior:constructor()
		self.name = "Wander"
		self.priority = 2
	end
	function WanderBehavior:canExecute(context)
		-- Wander if no target and not patrolling
		local _condition = not context.target
		if _condition then
			local _value = context.blackboard.patrolPoints
			_condition = not (_value ~= 0 and _value == _value and _value ~= "" and _value)
		end
		return _condition
	end
	function WanderBehavior:execute(context)
		local wanderTarget = context.blackboard.wanderTarget
		local _condition = context.blackboard.wanderTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local wanderTime = _condition
		local maxWanderTime = 5
		-- Generate new wander target if needed
		if not wanderTarget or wanderTime >= maxWanderTime then
			local newTarget = self:generateWanderTarget(context.position)
			context.blackboard.wanderTarget = newTarget
			context.blackboard.wanderTime = 0
		else
			context.blackboard.wanderTime = wanderTime + context.deltaTime
		end
		-- Move towards wander target
		local currentTarget = context.blackboard.wanderTarget
		if currentTarget then
			local distance = (context.position - currentTarget).Magnitude
			if distance > 2 then
				self:moveTowards(context, currentTarget)
			else
				-- Reached target, generate new one
				context.blackboard.wanderTarget = nil
				context.blackboard.wanderTime = maxWanderTime
			end
		end
		return {
			success = true,
			completed = false,
		}
	end
	function WanderBehavior:onEnter(context)
		print(`🚶 {context.entityId} is wandering around`)
		context.blackboard.wanderTarget = nil
		context.blackboard.wanderTime = 0
	end
	function WanderBehavior:generateWanderTarget(currentPosition)
		local wanderRadius = 15
		local randomAngle = math.random() * math.pi * 2
		local randomDistance = math.random() * wanderRadius
		local offset = Vector3.new(math.cos(randomAngle) * randomDistance, 0, math.sin(randomAngle) * randomDistance)
		return currentPosition + offset
	end
	function WanderBehavior:moveTowards(context, targetPosition)
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(clearTarget)
				humanoid.WalkSpeed = 6
			else
				self:smoothWanderMove(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothWanderMove(context.entity, clearTarget, context.deltaTime)
		end
	end
	function WanderBehavior:smoothWanderMove(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 6
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
return {
	IdleBehavior = IdleBehavior,
	FollowBehavior = FollowBehavior,
	PatrolBehavior = PatrolBehavior,
	InvestigateBehavior = InvestigateBehavior,
	FleeBehavior = FleeBehavior,
	AttackBehavior = AttackBehavior,
	WanderBehavior = WanderBehavior,
}
