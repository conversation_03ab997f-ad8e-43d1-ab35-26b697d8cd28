import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontext, AIBehaviorResult } from "../interfaces/AIBehavior";
export declare class Idle<PERSON>ehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(_context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private lookAround;
}
export declare class Follow<PERSON>ehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private moveTowards;
    private smoothMoveTo;
}
export declare class PatrolBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private generatePatrolPoints;
    private moveTowards;
    private smoothPatrolMove;
}
export declare class InvestigateBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private moveTowards;
    private smoothInvestigateMove;
    private lookAround;
}
export declare class FleeBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private smoothFleeMove;
}
export declare class AttackBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private moveTowards;
    private smoothAttackMove;
    private performAttack;
}
export declare class WanderBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private generateWanderTarget;
    private moveTowards;
    private smoothWanderMove;
}
