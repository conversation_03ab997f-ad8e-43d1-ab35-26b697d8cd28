import { DataStoreOptions, DataStoreResult } from "./interfaces/DataStoreConfig";
import { BasePlayerData } from "./interfaces/PlayerData";
import { BaseGlobalData } from "./interfaces/GlobalData";
export declare class DataStoreHelper<TPlayerData extends BasePlayerData = BasePlayerData, TGlobalData extends BaseGlobalData = BaseGlobalData> {
    private static instance;
    private playerDataStore?;
    private globalDataStore?;
    private cache;
    private options;
    private autoSaveConnection?;
    private constructor();
    static getInstance<T extends BasePlayerData = BasePlayerData, U extends BaseGlobalData = BaseGlobalData>(): DataStoreHelper<T, U>;
    configure(options: Partial<DataStoreOptions>): void;
    enableStudioTesting(): void;
    disableAutoSave(): void;
    loadPlayerData(userId: number): Promise<DataStoreResult<TPlayerData>>;
    savePlayerData(playerData: TPlayerData): Promise<DataStoreResult<boolean>>;
    updatePlayerData(userId: number, updates: Partial<TPlayerData>): Promise<DataStoreResult<TPlayerData>>;
    loadGlobalData(): Promise<DataStoreResult<TGlobalData>>;
    saveGlobalData(globalData: TGlobalData): Promise<DataStoreResult<boolean>>;
    updatePlayerGameData(userId: number, gameDataUpdates: Record<string, unknown>): Promise<DataStoreResult<TPlayerData>>;
    updateGlobalGameData(gameDataUpdates: Record<string, unknown>): Promise<DataStoreResult<TGlobalData>>;
    clearCache(): void;
    getCacheSize(): number;
    cleanup(): void;
    private retryOperation;
    private getFromCache;
    private setCache;
    private createDefaultPlayerData;
    private createDefaultGlobalData;
    private startAutoSave;
    private performAutoSave;
    private wait;
}
