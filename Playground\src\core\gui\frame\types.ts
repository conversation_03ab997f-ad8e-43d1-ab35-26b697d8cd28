import * as React from "@rbxts/react";

export interface BaseFrameProps {
  children?: React.ReactNode;
  size?: UDim2;
  position?: UDim2;
  anchorPoint?: Vector2;
  layoutOrder?: number;
  backgroundColor?: string;
  backgroundTransparency?: number;
  padding?: number;
  zIndex?: number;
  autoSize?: Enum.AutomaticSize;
  fillDirection?: "auto" | "manual"; // "auto" uses AutomaticSize, "manual" uses explicit size
}

export interface VerticalFrameProps extends BaseFrameProps {
  spacing?: number;
  horizontalAlignment?: Enum.HorizontalAlignment;
  verticalAlignment?: Enum.VerticalAlignment;
  fitContent?: boolean; // When true, frame sizes to fit its content
}

export interface HorizontalFrameProps extends BaseFrameProps {
  spacing?: number;
  horizontalAlignment?: Enum.HorizontalAlignment;
  verticalAlignment?: Enum.VerticalAlignment;
  fitContent?: boolean; // When true, frame sizes to fit its content
}

export interface ContainerFrameProps extends BaseFrameProps {
  cornerRadius?: number;
  borderColor?: string;
  borderThickness?: number;
  borderTransparency?: number;
  fitContent?: boolean; // When true, container sizes to fit its content
}

export interface ScrollingFrameProps extends BaseFrameProps {
  canvasSize?: UDim2;
  scrollBarThickness?: number;
  borderColor?: string;
  borderThickness?: number;
  cornerRadius?: number;
  scrollingDirection?: Enum.ScrollingDirection;
  elasticBehavior?: Enum.ElasticBehavior;
  automaticCanvasSize?: Enum.AutomaticSize;
}
