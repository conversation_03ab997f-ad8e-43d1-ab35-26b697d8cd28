import { Entity, EntitySpawnOptions } from "./interfaces/Entity";
import { EntityType } from "./enums/EntityType";
export declare class EntityManager {
    private static instance;
    private entities;
    private entityCounter;
    private heartbeatConnection?;
    private constructor();
    static getInstance(): EntityManager;
    spawnEntity(options: EntitySpawnOptions): Entity;
    getEntity(id: string): Entity | undefined;
    getEntitiesByType(entityType: EntityType): Entity[];
    getEntitiesInRadius(position: Vector3, radius: number): Entity[];
    updateEntityPosition(id: string, position: Vector3): void;
    updateEntityData(id: string, data: Record<string, unknown>): void;
    destroyEntity(id: string): boolean;
    getActiveEntityCount(): number;
    cleanup(): void;
    private generateEntityId;
    private createEntityInstance;
    private startHeartbeat;
}
