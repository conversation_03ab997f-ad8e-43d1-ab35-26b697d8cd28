export declare class PositionHelper {
    static setPosition(instance: Instance, position: Vector3, rotation?: Vector3): void;
    static ensurePrimaryPart(model: Model, position: Vector3, rotation?: Vector3): Part;
    static moveModel(model: Model, position: Vector3): void;
    static rotateModel(model: Model, rotation: Vector3): void;
    static getPosition(instance: Instance): Vector3;
    static getRotation(instance: Instance): Vector3;
    static offsetPosition(instance: Instance, offset: Vector3): Vector3;
    static lookAt(instance: Instance, target: Vector3): void;
    static randomizePosition(instance: Instance, range: Vector3): Vector3;
    static snapToGrid(instance: Instance, gridSize?: number): Vector3;
    static isInRange(instance1: Instance, instance2: Instance, range: number): boolean;
    static getDistance(instance1: Instance, instance2: Instance): number;
    static canMoveTo(from: Vector3, to: Vector3, ignoreList?: Instance[]): boolean;
    static findClearPath(from: Vector3, to: Vector3, ignoreList?: Instance[]): Vector3;
    static hasLineOfSight(from: Vector3, to: Vector3, ignoreList?: Instance[]): boolean;
}
