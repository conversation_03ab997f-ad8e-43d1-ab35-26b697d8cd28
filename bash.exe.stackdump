Stack trace:
Frame         Function      Args
0007FFFFA340  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA340, 0007FFFF9240) msys-2.0.dll+0x1FE8E
0007FFFFA340  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA618) msys-2.0.dll+0x67F9
0007FFFFA340  000210046832 (000210286019, 0007FFFFA1F8, 0007FFFFA340, 000000000000) msys-2.0.dll+0x6832
0007FFFFA340  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA340  000210068E24 (0007FFFFA350, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA620  00021006A225 (0007FFFFA350, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9A8440000 ntdll.dll
7FF9A7650000 KERNEL32.DLL
7FF9A5910000 KERNELBASE.dll
7FF9A70A0000 USER32.dll
7FF9A5620000 win32u.dll
7FF9A8340000 GDI32.dll
7FF9A57D0000 gdi32full.dll
7FF9A6110000 msvcp_win.dll
7FF9A5E40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9A67D0000 advapi32.dll
7FF9A73A0000 msvcrt.dll
7FF9A72F0000 sechost.dll
7FF9A78D0000 RPCRT4.dll
7FF9A4B30000 CRYPTBASE.DLL
7FF9A5D10000 bcryptPrimitives.dll
7FF9A79F0000 IMM32.DLL
