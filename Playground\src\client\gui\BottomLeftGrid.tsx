import * as React from "@rbxts/react";
import { Button, Modal, ListView, Label, VerticalFrame } from "../../core";
import { WorldTestingPanel } from "./WorldTestingPanel";
import { DebugPanel } from "./DebugPanel";

interface BottomLeftGridProps {
  onTestClick: () => void;
  onHelloClick: () => void;
}

export function BottomLeftGrid(_props: BottomLeftGridProps) {
  const [worldLabOpen, setWorldLabOpen] = React.useState(false);
  const [debugPanelOpen, setDebugPanelOpen] = React.useState(false);

  return (
    <>
      <VerticalFrame
        backgroundTransparency={1}
        size={new UDim2(0, 120, 0, 200)}
        position={new UDim2(0, 16, 1, -220)}
        anchorPoint={new Vector2(0, 1)}
        spacing={8}
        padding={0}
      >
 
        <Button
          text="🌍 World"
          onClick={() => setWorldLabOpen(true)}
          LayoutOrder={5}
        />

        <Button
          text="🔧 Debug"
          onClick={() => setDebugPanelOpen(true)}
          LayoutOrder={6}
        />
      </VerticalFrame>


      <WorldTestingPanel
        isOpen={worldLabOpen}
        onClose={() => setWorldLabOpen(false)}
      />

      <DebugPanel
        isOpen={debugPanelOpen}
        onClose={() => setDebugPanelOpen(false)}
      />
    </>
  );
}


