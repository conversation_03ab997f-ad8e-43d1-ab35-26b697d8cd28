import { AIBehavior, AIState, AIConfig } from "./interfaces/AIBehavior";
export declare class AIController {
    private static instance;
    private entityManager;
    private aiEntities;
    private heartbeatConnection?;
    private lastUpdateTime;
    private constructor();
    static getInstance(): AIController;
    registerAI(entityId: string, config?: Partial<AIConfig>): AIAgent;
    unregisterAI(entityId: string): void;
    getAI(entityId: string): AIAgent | undefined;
    getAllAIs(): AIAgent[];
    getAICount(): number;
    private startAILoop;
    cleanup(): void;
}
export declare class AIAgent {
    private entityId;
    private entity;
    private config;
    private behaviors;
    private currentBehavior?;
    private state;
    private blackboard;
    private lastStateChange;
    constructor(entityId: string, entity: Instance, config?: Partial<AIConfig>);
    addBehavior(behavior: AIBehavior): void;
    removeBehavior(behaviorName: string): void;
    setState(newState: AIState): void;
    getState(): AIState;
    setBlackboardValue(key: string, value: unknown): void;
    getBlackboardValue(key: string): unknown;
    update(deltaTime: number): void;
    private selectBehavior;
    private findNearestPlayer;
    private blackboardToRecord;
    private registerDefaultBehaviors;
    cleanup(): void;
}
