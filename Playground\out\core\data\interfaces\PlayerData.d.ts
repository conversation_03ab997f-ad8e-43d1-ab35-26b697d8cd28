export interface CorePlayerData {
    userId: number;
    lastLogin: number;
    playtime: number;
    createdAt: number;
    version: string;
}
export interface BasePlayerData extends CorePlayerData {
    gameData: Record<string, unknown>;
    settings: PlayerSettings;
    metadata: PlayerMetadata;
}
export interface PlayerSettings {
    musicVolume: number;
    sfxVolume: number;
    language: string;
    [key: string]: unknown;
}
export interface PlayerMetadata {
    totalSessions: number;
    averageSessionTime: number;
    lastSaveTime: number;
    dataVersion: number;
    [key: string]: unknown;
}
export type GamePlayerData<T = Record<string, unknown>> = BasePlayerData & {
    gameData: T;
};
